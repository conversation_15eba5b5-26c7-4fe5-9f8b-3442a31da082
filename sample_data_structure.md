# Sample Data Structure for Google Sheets Data Migration

This document provides examples of how your source Google Sheets should be structured for the data migration tool to work correctly.

## Source Spreadsheet Structure

Each sheet in your source spreadsheet should follow this structure:

### Row 1: Frequency Information

The first row should contain frequency information in the first cell (A1). The tool looks for keywords "DAILY" or "WEEK" to determine the email frequency.

**Examples:**
- `Email DAILY - Client Communications`
- `Email once in a Week - Newsletter Subscribers`
- `DAILY Email - Important Clients`

### Row 2: Column Headers

The second row should contain the column headers. The tool specifically looks for these headers (case-insensitive):

- Name
- Email
- Date
- Amount

**Example:**

| Name | Email | Date | Amount | Other Info | Notes |
|------|-------|------|--------|------------|-------|

### Row 3 and beyond: Data Rows

From the third row onward, each row should contain the actual data:

**Example:**

| <PERSON> | <EMAIL> | 15.04.22, 22.04.22 | $1,500 | Gold tier | Follow up |
|------------|------------------|-------------------|---------|-----------|------------|
| Jane Doe | <EMAIL> | 10.05.22 | $2,750.50 | Silver tier | |

## Data Format Details

### Name
- Any text format is accepted
- Will be converted to uppercase during processing

### Email
- Standard email format

### Date
- Can be a single date or multiple comma-separated dates
- Supported formats: DD.MM.YY, DD.MM.YYYY, DD/MM/YY, DD/MM/YYYY
- For multiple dates, the most recent date will be used for calculations

**Examples:**
- `15.04.22`
- `15.04.2022`
- `15/04/22`
- `15.04.22, 22.04.22, 30.04.22`

### Amount
- Can include currency symbols ($) and commas
- Will be parsed to a numeric value

**Examples:**
- `$1,500`
- `1500`
- `2,750.50`

## Sheet Naming

Each sheet name is used to extract the company name. For best results:

- Use clear, descriptive names for your sheets
- The script will clean up sheet names by removing numbers and special characters at the end

**Examples:**
- Sheet named "Acme Corp" → Company will be "Acme Corp"
- Sheet named "Acme Corp_2023" → Company will be "Acme Corp"
- Sheet named "Acme Corp-01" → Company will be "Acme Corp"

## Next Email Date Calculation

The script will calculate the next email date based on:

1. The frequency in Row 1 (daily or weekly)
2. The most recent date in the Date column

This calculated date will be written to column H in the source spreadsheet.

## Example Sheet

Here's a complete example of how a sheet should look:

```
A1: Email DAILY - Client Communications
```

| Name | Email | Date | Amount | Other Info | Notes | | Next Email |
|------|-------|------|--------|------------|-------|-------------||
| JOHN SMITH | <EMAIL> | 15.04.22, 22.04.22 | $1,500 | Gold tier | Follow up | | 23.04.22 |
| JANE DOE | <EMAIL> | 10.05.22 | $2,750.50 | Silver tier | | | 11.05.22 |
| ROBERT BROWN | <EMAIL> | 05.05.22, 12.05.22 | $950 | Bronze tier | New client | | 13.05.22 |

After processing, column H will contain the calculated next email dates based on the frequency and the most recent date for each contact.