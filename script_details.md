Create a Python script for Google Sheets data migration with the following specifications:

  CORE FUNCTIONALITY:  
- Process multiple broker sheets from a source Google Spreadsheet
- Migrate data to a target spreadsheet with formatting and summary
- Calculate and update next email dates in source spreadsheet

  TECHNICAL REQUIREMENTS:  
1.Libraries  : Use gspread, google.colab.auth, pandas, gradio, datetime, collections.defaultdict
2.Authentication  : Google Colab authentication with default credentials
3.Interface  : Modern Gradio web interface with professional styling

  DATA PROCESSING LOGIC: 
- Ask user to enter source spreadsheet name or ID (ID recommanded)
- Source sheets structure: Row 1 contains frequency ("Email DAILY - [some other text]" or "Email once in a Week - [some other text]"), Row 2 contains headers (Name, Email, Date, Amount), Data starts from Row 3
- Process columns: Name (uppercase), Email, Date (comma-separated DD.MM.YY format), Amount (with $ and comma handling)
- Calculate: Total emails count, First email date, Recent email date, Next email date
- Next email date calculation: If "DAILY" in row 1 → add 1 day to recent date, If "WEEK" in row 1 → add 7 days to recent date
- Update source spreadsheet column H with calculated next email dates

  TARGET SPREADSHEET FEATURES:
- Ask user to enter target spreadsheet name or ID (if not exists create new)  
- Create 'WD_Email_Details' worksheet with headers: Company, Person, Email, Total Emails, First Email, Recent Email, Amount ($)
- Apply professional formatting: colored headers, currency formatting for amounts, auto-resize columns
- Create 'Summary' sheet with company statistics and charts
- Remove empty default 'Sheet1' if exists

  GRADIO INTERFACE REQUIREMENTS:  
- Modern theme with gradient styling and professional CSS
- Input fields for source and target spreadsheet ID/names
- Real-time progress tracking with percentage display
- Live processing logs with timestamps
- Status updates during processing
- Beautiful HTML summary table with company statistics
- Error handling and validation

  ADVANCED FEATURES:  
- Progress tracking with global variables
- Date parsing for multiple formats (DD.MM.YY and DD.MM.YYYY)
- Amount parsing with $ and comma removal
- Company-wise summary statistics
- Processing logs saved to JSON
- Automatic chart generation in summary sheets
- Error recovery and graceful failure handling

  UI STYLING:  
- Gradient backgrounds, hover effects, professional color scheme
- Real-time updates without page refresh
- Mobile-responsive design
- Status boxes with monospace font for logs
- Success/error indicators with emojis

  OUTPUT REQUIREMENTS:  
- Processing summary with total records, companies, and amounts
- Clickable spreadsheet URLs
- Detailed success/error messages
- HTML tables with sorting and styling
- Export-ready summary reports

  ERROR HANDLING:  
- Validate spreadsheet access
- Handle missing columns gracefully
- Continue processing if individual sheets fail
- Display specific error messages
- Fallback mechanisms for date/amount parsing

Please include comprehensive comments, progress indicators, and make the code production-ready with proper exception handling.