# Google Sheets Data Migration Tool

This tool processes multiple broker sheets from a source Google Spreadsheet and migrates data to a target spreadsheet with formatting and summary. It's designed to run in Google Colab with a modern Gradio web interface.

## Features

- Process multiple broker sheets from a source Google Spreadsheet
- Migrate data to a target spreadsheet with professional formatting
- Calculate and update next email dates in the source spreadsheet
- Create summary statistics and charts
- Modern Gradio web interface with real-time progress tracking

## Requirements

- Google Colab environment
- The following Python libraries:
  - gspread
  - pandas
  - gradio
  - google.colab
  - datetime
  - collections.defaultdict

## How to Use

### 1. Setup in Google Colab

1. Create a new Google Colab notebook
2. Upload the `google_sheets_migration.py` script to your Colab environment
3. Run the following code in a Colab cell:

```python
# Install required packages
!pip install gspread pandas gradio

# Import and run the script
%run google_sheets_migration.py
```

### 2. Using the Interface

1. The script will authenticate with your Google account
2. Enter the source spreadsheet ID or name (ID is recommended for better reliability)
3. Optionally enter a target spreadsheet ID or name (leave empty to create a new spreadsheet)
4. Click "Start Processing"
5. Monitor the progress in real-time
6. View the processing logs and summary when complete

### 3. Source Spreadsheet Requirements

Your source spreadsheet should have the following structure:

- **Row 1**: Contains frequency information ("Email DAILY - [some text]" or "Email once in a Week - [some text]")
- **Row 2**: Contains headers (Name, Email, Date, Amount)
- **Row 3+**: Data rows

### 4. Output

The script will create or update a target spreadsheet with:

- **WD_Email_Details worksheet**: Contains all processed data with formatting
- **Summary worksheet**: Contains company statistics and a chart

## Data Processing Logic

- Names are converted to uppercase
- Dates are parsed from various formats (DD.MM.YY, DD.MM.YYYY, etc.)
- Amounts are parsed with $ and comma handling
- Next email dates are calculated based on frequency (daily or weekly)
- Source spreadsheet column H is updated with calculated next email dates

## Troubleshooting

- If authentication fails, make sure you're logged into the correct Google account
- If spreadsheet access fails, check that you have permission to access the spreadsheets
- Check the error logs for specific issues during processing

## License

This project is licensed for use as specified by the project owner.

---

For more details, refer to the `script_details.md` file.