{"cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github"}, "source": ["# Google Sheets Data Migration Tool\n", "\n", "This notebook demonstrates how to use the Google Sheets Data Migration Tool to process broker sheets and migrate data with formatting and summary.\n", "\n", "## Overview\n", "\n", "The tool performs the following tasks:\n", "\n", "- Process multiple broker sheets from a source Google Spreadsheet\n", "- Migrate data to a target spreadsheet with formatting and summary\n", "- Calculate and update next email dates in source spreadsheet\n", "- Create professional summary sheets with charts\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Install Required Libraries\n", "\n", "First, let's install the necessary libraries for the script to run."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install gspread pandas gradio"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Upload the Script\n", "\n", "You can either upload the script using the file upload button in the left sidebar, or you can run the cell below to download it directly from your GitHub repository (if available).\n", "\n", "### Option 1: Manual Upload\n", "\n", "Use the file upload button in the left sidebar to upload `google_sheets_migration.py`.\n", "\n", "### Option 2: Download from Repository\n", "\n", "Uncomment and run the cell below if you have the script in a GitHub repository."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Uncomment and modify the URL to download from your repository\n", "# !wget https://raw.githubusercontent.com/yourusername/yourrepository/main/google_sheets_migration.py"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: <PERSON> the Script\n", "\n", "Now let's run the script to start the Gradio interface."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%run google_sheets_migration.py"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Using the Interface\n", "\n", "After running the script, you'll see a Gradio interface with the following components:\n", "\n", "1. **Source Spreadsheet**: Enter the ID or name of your source spreadsheet (ID is recommended)\n", "2. **Target Spreadsheet**: Enter the ID or name of your target spreadsheet (leave empty to create a new one)\n", "3. **Start Processing**: Click this button to begin the data migration process\n", "4. **Processing Logs**: Real-time logs of the processing steps\n", "5. **Processing Summary**: A summary of the results after processing is complete\n", "\n", "### How to Find Your Spreadsheet ID\n", "\n", "The spreadsheet ID is the long string of characters in the URL of your Google Sheet. For example, in the URL:\n", "\n", "```\n", "https://docs.google.com/spreadsheets/d/1a2b3c4d5e6f7g8h9i0j1k2l3m4n5o6p7q8r9s0t1u2v3/edit#gid=0\n", "```\n", "\n", "The ID is: `1a2b3c4d5e6f7g8h9i0j1k2l3m4n5o6p7q8r9s0t1u2v3`\n", "\n", "### Source Spreadsheet Requirements\n", "\n", "Your source spreadsheet should have the following structure:\n", "\n", "- **Row 1**: Contains frequency information (\"Email DAILY - [some text]\" or \"Email once in a Week - [some text]\")\n", "- **Row 2**: Contains headers (Name, Email, Date, Amount)\n", "- **Row 3+**: Data rows\n", "\n", "### Output\n", "\n", "The script will create or update a target spreadsheet with:\n", "\n", "- **WD_Email_Details worksheet**: Contains all processed data with formatting\n", "- **Summary worksheet**: Contains company statistics and a chart"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Troubleshooting\n", "\n", "If you encounter any issues, check the following:\n", "\n", "1. **Authentication**: Make sure you're logged into the correct Google account\n", "2. **Spreadsheet Access**: Verify that you have permission to access the spreadsheets\n", "3. **Data Format**: Ensure your source spreadsheet follows the required structure\n", "4. **Error Logs**: Check the processing logs for specific error messages\n", "\n", "If you need to restart the process, you can run the script again by executing the cell in Step 3."]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}