# Quick Start Guide: Google Sheets Data Migration Tool

## Overview

This tool helps you process multiple broker sheets from a source Google Spreadsheet and migrate the data to a target spreadsheet with professional formatting and summary statistics.

## Getting Started in 5 Minutes

### Step 1: Open Google Colab

1. Go to [Google Colab](https://colab.research.google.com/)
2. Sign in with your Google account
3. Click on **File** > **Upload notebook** and upload the `WD_Data_Migration.ipynb` file

### Step 2: Run the Setup

1. Run the first code cell to install required libraries:
   ```python
   !pip install gspread pandas gradio
   ```

2. Upload the `google_sheets_migration.py` script using the file upload button in the left sidebar

3. Run the script execution cell:
   ```python
   %run google_sheets_migration.py
   ```

### Step 3: Use the Interface

1. When prompted, authenticate with your Google account
2. Enter your source spreadsheet ID or name (ID is recommended)
3. Optionally enter a target spreadsheet ID or name (leave empty to create a new one)
4. Click **Start Processing**
5. Monitor the progress and view the results

## Finding Your Spreadsheet ID

The spreadsheet ID is in the URL of your Google Sheet:

```
https://docs.google.com/spreadsheets/d/YOUR_SPREADSHEET_ID_HERE/edit
```

## Source Spreadsheet Requirements

Ensure your source spreadsheet follows this structure:

- **Row 1**: Contains frequency information ("Email DAILY - [text]" or "Email once in a Week - [text]")
- **Row 2**: Contains headers (Name, Email, Date, Amount)
- **Row 3+**: Data rows

See `sample_data_structure.md` for detailed examples.

## What Happens During Processing

1. The tool reads all sheets in your source spreadsheet
2. It processes the data according to the specified requirements
3. It calculates next email dates based on frequency and updates the source spreadsheet
4. It creates or updates the target spreadsheet with:
   - A `WD_Email_Details` worksheet containing all processed data
   - A `Summary` worksheet with company statistics and a chart

## Troubleshooting

- **Authentication Issues**: Make sure you're logged into the correct Google account
- **Access Denied**: Verify you have permission to access the spreadsheets
- **Processing Errors**: Check the logs for specific error messages

## Need More Help?

Refer to these resources:

- `README.md`: Comprehensive documentation
- `sample_data_structure.md`: Detailed examples of expected data formats
- `script_details.md`: Technical specifications of the script