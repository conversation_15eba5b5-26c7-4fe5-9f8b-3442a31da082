# Google Sheets Data Migration Script
# This script processes multiple broker sheets from a source Google Spreadsheet
# and migrates data to a target spreadsheet with formatting and summary

import gspread
import pandas as pd
import gradio as gr
import datetime
import json
import re
import time
import numpy as np
from collections import defaultdict
from google.colab import auth
from datetime import datetime, timedelta
from IPython.display import display, HTML

# Global variables for progress tracking
processing_status = {
    'total_sheets': 0,
    'processed_sheets': 0,
    'total_records': 0,
    'processed_records': 0,
    'current_sheet': '',
    'companies_processed': set(),
    'total_amount': 0,
    'logs': [],
    'errors': [],
    'start_time': None,
    'end_time': None
}

# Helper function to log messages with timestamps
def log_message(message, error=False):
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_entry = {"timestamp": timestamp, "message": message, "type": "error" if error else "info"}
    processing_status['logs'].append(log_entry)
    
    if error:
        processing_status['errors'].append(log_entry)
    
    log_text = f"[{timestamp}] {'ERROR: ' if error else ''}{message}"
    return log_text

# Function to parse dates in different formats
def parse_date(date_str):
    if not date_str or pd.isna(date_str):
        return None
    
    date_str = str(date_str).strip()
    
    # Try different date formats
    formats = ["%d.%m.%y", "%d.%m.%Y", "%d/%m/%y", "%d/%m/%Y", "%Y-%m-%d"]
    
    for fmt in formats:
        try:
            return datetime.strptime(date_str, fmt).date()
        except ValueError:
            continue
    
    # If comma-separated dates, take the most recent one
    if ',' in date_str:
        try:
            date_parts = [part.strip() for part in date_str.split(',')]
            parsed_dates = []
            
            for part in date_parts:
                for fmt in formats:
                    try:
                        parsed_dates.append(datetime.strptime(part, fmt).date())
                        break
                    except ValueError:
                        continue
            
            if parsed_dates:
                return max(parsed_dates)
        except Exception as e:
            log_message(f"Error parsing comma-separated date '{date_str}': {str(e)}", error=True)
    
    log_message(f"Could not parse date: '{date_str}'", error=True)
    return None

# Function to parse amount values
def parse_amount(amount_str):
    if not amount_str or pd.isna(amount_str):
        return 0.0
    
    # Convert to string if not already
    amount_str = str(amount_str).strip()
    
    # Remove $ and commas
    amount_str = amount_str.replace('$', '').replace(',', '')
    
    try:
        return float(amount_str)
    except ValueError:
        log_message(f"Could not parse amount: '{amount_str}'", error=True)
        return 0.0

# Function to calculate next email date based on frequency
def calculate_next_email_date(frequency, recent_date):
    if not recent_date:
        return None
    
    if isinstance(recent_date, str):
        recent_date = parse_date(recent_date)
        if not recent_date:
            return None
    
    frequency = str(frequency).upper()
    
    if "DAILY" in frequency:
        return recent_date + timedelta(days=1)
    elif "WEEK" in frequency:
        return recent_date + timedelta(days=7)
    else:
        # Default to weekly if frequency not recognized
        log_message(f"Unrecognized frequency: '{frequency}', defaulting to weekly", error=True)
        return recent_date + timedelta(days=7)

# Function to format date for Google Sheets
def format_date_for_sheets(date_obj):
    if not date_obj:
        return ""
    
    if isinstance(date_obj, str):
        date_obj = parse_date(date_obj)
        if not date_obj:
            return ""
    
    return date_obj.strftime("%d.%m.%Y")

# Function to extract company name from sheet title
def extract_company_name(sheet_title):
    # Remove common prefixes/suffixes and clean up
    company = sheet_title.strip()
    
    # Remove any numbers or special characters at the end
    company = re.sub(r'[\d_\-]+$', '', company).strip()
    
    return company

# Main processing function
def process_spreadsheets(source_spreadsheet_id, target_spreadsheet_id, progress=None):
    # Reset processing status
    processing_status['total_sheets'] = 0
    processing_status['processed_sheets'] = 0
    processing_status['total_records'] = 0
    processing_status['processed_records'] = 0
    processing_status['current_sheet'] = ''
    processing_status['companies_processed'] = set()
    processing_status['total_amount'] = 0
    processing_status['logs'] = []
    processing_status['errors'] = []
    processing_status['start_time'] = datetime.now()
    
    log_message("Starting authentication with Google...")
    
    try:
        # Authenticate with Google
        auth.authenticate_user()

        # Get the credentials from Google Colab
        from google.auth import default
        creds, _ = default()

        # Authorize gspread with the credentials
        gc = gspread.authorize(creds)
        log_message("Authentication successful")
    except Exception as e:
        error_msg = f"Authentication failed: {str(e)}"
        log_message(error_msg, error=True)
        return {"success": False, "message": error_msg, "logs": processing_status['logs']}
    
    try:
        # Open source spreadsheet
        log_message(f"Opening source spreadsheet: {source_spreadsheet_id}")
        source_spreadsheet = gc.open_by_key(source_spreadsheet_id) if source_spreadsheet_id.strip() else gc.open(source_spreadsheet_id)
        log_message(f"Successfully opened source spreadsheet: {source_spreadsheet.title}")
        
        # Get all worksheets from source
        source_worksheets = source_spreadsheet.worksheets()
        processing_status['total_sheets'] = len(source_worksheets)
        log_message(f"Found {len(source_worksheets)} sheets in source spreadsheet")
        
        # Try to open target spreadsheet or create if it doesn't exist
        try:
            target_spreadsheet = gc.open_by_key(target_spreadsheet_id) if target_spreadsheet_id.strip() else gc.open(target_spreadsheet_id)
            log_message(f"Opened existing target spreadsheet: {target_spreadsheet.title}")
        except:
            log_message(f"Target spreadsheet not found, creating new spreadsheet")
            target_spreadsheet = gc.create("WD_Data_Migration_" + datetime.now().strftime("%Y%m%d_%H%M%S"))
            log_message(f"Created new target spreadsheet: {target_spreadsheet.title}")
        
        # Create or clear the WD_Email_Details worksheet in target
        try:
            email_details_sheet = target_spreadsheet.worksheet('WD_Email_Details')
            email_details_sheet.clear()
            log_message("Cleared existing WD_Email_Details worksheet")
        except gspread.exceptions.WorksheetNotFound:
            email_details_sheet = target_spreadsheet.add_worksheet(title='WD_Email_Details', rows=1000, cols=20)
            log_message("Created new WD_Email_Details worksheet")
        
        # Set up headers for the email details sheet
        headers = ["Company", "Person", "Email", "Total Emails", "First Email", "Recent Email", "Next Email", "Amount ($)"]
        email_details_sheet.update('A1:H1', [headers])
        
        # Apply formatting to headers
        header_format = {
            "backgroundColor": {"red": 0.2, "green": 0.4, "blue": 0.6},
            "textFormat": {"foregroundColor": {"red": 1.0, "green": 1.0, "blue": 1.0}, "bold": True},
            "horizontalAlignment": "CENTER"
        }
        
        format_requests = {
            "requests": [{
                "repeatCell": {
                    "range": {"sheetId": email_details_sheet.id, "startRowIndex": 0, "endRowIndex": 1},
                    "cell": {"userEnteredFormat": header_format},
                    "fields": "userEnteredFormat(backgroundColor,textFormat,horizontalAlignment)"
                }
            }]
        }
        
        target_spreadsheet.batch_update(format_requests)
        log_message("Applied formatting to headers")
        
        # Process each worksheet in the source spreadsheet
        all_data = []
        company_stats = defaultdict(lambda: {'total_records': 0, 'total_amount': 0.0, 'emails': set()})
        
        for i, worksheet in enumerate(source_worksheets):
            processing_status['current_sheet'] = worksheet.title
            processing_status['processed_sheets'] = i + 1
            
            try:
                log_message(f"Processing sheet: {worksheet.title} ({i+1}/{len(source_worksheets)})")
                
                # Get all values from the worksheet
                all_values = worksheet.get_all_values()
                
                if len(all_values) < 3:  # Need at least header row, column names row, and one data row
                    log_message(f"Skipping sheet {worksheet.title}: Not enough data rows", error=True)
                    continue
                
                # Extract frequency from first row
                frequency = all_values[0][0] if all_values and all_values[0] else ""
                
                # Extract headers from second row
                headers = all_values[1] if len(all_values) > 1 else []
                
                # Find required column indices
                name_col = next((i for i, h in enumerate(headers) if h and 'name' in h.lower()), None)
                email_col = next((i for i, h in enumerate(headers) if h and 'email' in h.lower()), None)
                date_col = next((i for i, h in enumerate(headers) if h and 'date' in h.lower()), None)
                amount_col = next((i for i, h in enumerate(headers) if h and 'amount' in h.lower()), None)
                
                if None in (name_col, email_col, date_col, amount_col):
                    missing = []
                    if name_col is None: missing.append("Name")
                    if email_col is None: missing.append("Email")
                    if date_col is None: missing.append("Date")
                    if amount_col is None: missing.append("Amount")
                    log_message(f"Skipping sheet {worksheet.title}: Missing required columns: {', '.join(missing)}", error=True)
                    continue
                
                # Process data rows (starting from row 3)
                data_rows = all_values[2:] if len(all_values) > 2 else []
                processing_status['total_records'] += len(data_rows)
                
                # Extract company name from sheet title
                company = extract_company_name(worksheet.title)
                processing_status['companies_processed'].add(company)
                
                # Process each person's data
                for row_idx, row in enumerate(data_rows):
                    if len(row) <= max(name_col, email_col, date_col, amount_col):
                        log_message(f"Skipping row {row_idx+3} in sheet {worksheet.title}: Row too short", error=True)
                        continue
                    
                    try:
                        name = row[name_col].strip().upper() if row[name_col] else ""
                        email = row[email_col].strip() if row[email_col] else ""
                        date_str = row[date_col].strip() if row[date_col] else ""
                        amount_str = row[amount_col].strip() if row[amount_col] else "0"
                        
                        if not (name and email and date_str):
                            log_message(f"Skipping row {row_idx+3} in sheet {worksheet.title}: Missing required data", error=True)
                            continue
                        
                        # Parse dates
                        dates = [parse_date(d.strip()) for d in date_str.split(',') if d.strip()]
                        dates = [d for d in dates if d is not None]
                        
                        if not dates:
                            log_message(f"Skipping row {row_idx+3} in sheet {worksheet.title}: No valid dates found", error=True)
                            continue
                        
                        first_date = min(dates)
                        recent_date = max(dates)
                        total_emails = len(dates)
                        
                        # Calculate next email date
                        next_email_date = calculate_next_email_date(frequency, recent_date)
                        
                        # Parse amount
                        amount = parse_amount(amount_str)
                        processing_status['total_amount'] += amount
                        
                        # Add to company stats
                        company_stats[company]['total_records'] += 1
                        company_stats[company]['total_amount'] += amount
                        company_stats[company]['emails'].add(email)
                        
                        # Add to all data
                        all_data.append([
                            company,
                            name,
                            email,
                            total_emails,
                            format_date_for_sheets(first_date),
                            format_date_for_sheets(recent_date),
                            format_date_for_sheets(next_email_date),
                            amount
                        ])
                        
                        # Update next email date in source spreadsheet (column H)
                        if next_email_date:
                            try:
                                next_email_cell = f"H{row_idx+3}"
                                worksheet.update(next_email_cell, format_date_for_sheets(next_email_date))
                            except Exception as e:
                                log_message(f"Failed to update next email date in source sheet: {str(e)}", error=True)
                        
                        processing_status['processed_records'] += 1
                        
                        # Update progress
                        if progress:
                            progress_pct = (processing_status['processed_records'] / max(1, processing_status['total_records'])) * 100
                            progress(progress_pct, f"Processed {processing_status['processed_records']} of {processing_status['total_records']} records")
                    
                    except Exception as e:
                        log_message(f"Error processing row {row_idx+3} in sheet {worksheet.title}: {str(e)}", error=True)
                
                log_message(f"Completed processing sheet: {worksheet.title}")
            
            except Exception as e:
                log_message(f"Error processing sheet {worksheet.title}: {str(e)}", error=True)
        
        # Update the email details sheet with all data
        if all_data:
            email_details_sheet.update('A2', all_data)
            log_message(f"Updated WD_Email_Details with {len(all_data)} records")
            
            # Apply formatting to the data
            format_requests = {
                "requests": [
                    # Format amount column as currency
                    {
                        "repeatCell": {
                            "range": {
                                "sheetId": email_details_sheet.id,
                                "startRowIndex": 1,
                                "endRowIndex": 1 + len(all_data),
                                "startColumnIndex": 7,
                                "endColumnIndex": 8
                            },
                            "cell": {
                                "userEnteredFormat": {
                                    "numberFormat": {"type": "CURRENCY", "pattern": "$#,##0.00"}
                                }
                            },
                            "fields": "userEnteredFormat.numberFormat"
                        }
                    },
                    # Auto-resize columns
                    {
                        "autoResizeDimensions": {
                            "dimensions": {
                                "sheetId": email_details_sheet.id,
                                "dimension": "COLUMNS",
                                "startIndex": 0,
                                "endIndex": 8
                            }
                        }
                    }
                ]
            }
            
            target_spreadsheet.batch_update(format_requests)
            log_message("Applied formatting to data and auto-resized columns")
        else:
            log_message("No valid data found to update target spreadsheet", error=True)
        
        # Create or clear the Summary sheet
        try:
            summary_sheet = target_spreadsheet.worksheet('Summary')
            summary_sheet.clear()
            log_message("Cleared existing Summary worksheet")
        except gspread.exceptions.WorksheetNotFound:
            summary_sheet = target_spreadsheet.add_worksheet(title='Summary', rows=100, cols=10)
            log_message("Created new Summary worksheet")
        
        # Update Summary sheet with company statistics
        summary_headers = ["Company", "Total Records", "Unique Emails", "Total Amount ($)"]
        summary_sheet.update('A1:D1', [summary_headers])
        
        summary_data = []
        for company, stats in company_stats.items():
            summary_data.append([
                company,
                stats['total_records'],
                len(stats['emails']),
                stats['total_amount']
            ])
        
        if summary_data:
            summary_sheet.update('A2', summary_data)
            log_message(f"Updated Summary sheet with {len(summary_data)} company records")
            
            # Apply formatting to Summary sheet
            format_requests = {
                "requests": [
                    # Format headers
                    {
                        "repeatCell": {
                            "range": {"sheetId": summary_sheet.id, "startRowIndex": 0, "endRowIndex": 1},
                            "cell": {"userEnteredFormat": header_format},
                            "fields": "userEnteredFormat(backgroundColor,textFormat,horizontalAlignment)"
                        }
                    },
                    # Format amount column as currency
                    {
                        "repeatCell": {
                            "range": {
                                "sheetId": summary_sheet.id,
                                "startRowIndex": 1,
                                "endRowIndex": 1 + len(summary_data),
                                "startColumnIndex": 3,
                                "endColumnIndex": 4
                            },
                            "cell": {
                                "userEnteredFormat": {
                                    "numberFormat": {"type": "CURRENCY", "pattern": "$#,##0.00"}
                                }
                            },
                            "fields": "userEnteredFormat.numberFormat"
                        }
                    },
                    # Auto-resize columns
                    {
                        "autoResizeDimensions": {
                            "dimensions": {
                                "sheetId": summary_sheet.id,
                                "dimension": "COLUMNS",
                                "startIndex": 0,
                                "endIndex": 4
                            }
                        }
                    }
                ]
            }
            
            target_spreadsheet.batch_update(format_requests)
            log_message("Applied formatting to Summary sheet")
            
            # Create chart in Summary sheet
            try:
                chart_requests = {
                    "requests": [{
                        "addChart": {
                            "chart": {
                                "spec": {
                                    "title": "Company Records Distribution",
                                    "pieChart": {
                                        "legendPosition": "RIGHT_LEGEND",
                                        "domain": {
                                            "sourceRange": {
                                                "sources": [{
                                                    "sheetId": summary_sheet.id,
                                                    "startRowIndex": 1,
                                                    "endRowIndex": 1 + len(summary_data),
                                                    "startColumnIndex": 0,
                                                    "endColumnIndex": 1
                                                }]
                                            }
                                        },
                                        "series": {
                                            "sourceRange": {
                                                "sources": [{
                                                    "sheetId": summary_sheet.id,
                                                    "startRowIndex": 1,
                                                    "endRowIndex": 1 + len(summary_data),
                                                    "startColumnIndex": 1,
                                                    "endColumnIndex": 2
                                                }]
                                            }
                                        }
                                    }
                                },
                                "position": {
                                    "overlayPosition": {
                                        "anchorCell": {
                                            "sheetId": summary_sheet.id,
                                            "rowIndex": 1,
                                            "columnIndex": 5
                                        },
                                        "widthPixels": 400,
                                        "heightPixels": 300
                                    }
                                }
                            }
                        }
                    }]
                }
                
                target_spreadsheet.batch_update(chart_requests)
                log_message("Added chart to Summary sheet")
            except Exception as e:
                log_message(f"Failed to create chart: {str(e)}", error=True)
        
        # Remove default Sheet1 if it exists
        try:
            sheet1 = target_spreadsheet.worksheet('Sheet1')
            target_spreadsheet.del_worksheet(sheet1)
            log_message("Removed default Sheet1")
        except gspread.exceptions.WorksheetNotFound:
            pass
        
        processing_status['end_time'] = datetime.now()
        duration = (processing_status['end_time'] - processing_status['start_time']).total_seconds()
        
        # Create success summary
        summary = {
            "success": True,
            "source_spreadsheet": source_spreadsheet.title,
            "target_spreadsheet": target_spreadsheet.title,
            "target_spreadsheet_url": f"https://docs.google.com/spreadsheets/d/{target_spreadsheet.id}",
            "total_sheets": processing_status['total_sheets'],
            "processed_sheets": processing_status['processed_sheets'],
            "total_records": processing_status['total_records'],
            "processed_records": processing_status['processed_records'],
            "companies_processed": len(processing_status['companies_processed']),
            "total_amount": processing_status['total_amount'],
            "duration_seconds": duration,
            "logs": processing_status['logs'],
            "errors": len(processing_status['errors'])
        }
        
        log_message(f"Processing completed in {duration:.2f} seconds")
        return summary
    
    except Exception as e:
        error_msg = f"Processing failed: {str(e)}"
        log_message(error_msg, error=True)
        processing_status['end_time'] = datetime.now()
        duration = (processing_status['end_time'] - processing_status['start_time']).total_seconds()
        
        return {
            "success": False,
            "message": error_msg,
            "duration_seconds": duration,
            "logs": processing_status['logs'],
            "errors": processing_status['errors']
        }

# Function to create Gradio interface
def create_interface():
    # CSS for styling
    css = """
    .container {
        max-width: 900px;
        margin: 0 auto;
        padding: 20px;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    .header {
        background: linear-gradient(135deg, #4a69bd, #6a89cc);
        color: white;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
        text-align: center;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .header h1 {
        margin: 0;
        font-size: 24px;
    }
    .header p {
        margin: 10px 0 0;
        opacity: 0.9;
    }
    .input-section {
        background: white;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }
    .progress-section {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }
    .log-section {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
        max-height: 300px;
        overflow-y: auto;
        font-family: monospace;
        font-size: 14px;
        line-height: 1.5;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }
    .summary-section {
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }
    .summary-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 15px;
    }
    .summary-table th {
        background: #4a69bd;
        color: white;
        padding: 10px;
        text-align: left;
    }
    .summary-table td {
        padding: 10px;
        border-bottom: 1px solid #ddd;
    }
    .summary-table tr:hover {
        background: #f5f5f5;
    }
    .error {
        color: #e74c3c;
    }
    .success {
        color: #2ecc71;
    }
    .info {
        color: #3498db;
    }
    .warning {
        color: #f39c12;
    }
    .btn {
        background: linear-gradient(135deg, #4a69bd, #6a89cc);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        font-size: 16px;
        transition: all 0.3s ease;
    }
    .btn:hover {
        background: linear-gradient(135deg, #3d5aa9, #5a79bc);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    .btn:active {
        transform: translateY(0);
    }
    .spreadsheet-link {
        color: #3498db;
        text-decoration: none;
        font-weight: bold;
    }
    .spreadsheet-link:hover {
        text-decoration: underline;
    }
    """
    
    # HTML template for the interface
    html_template = """
    <div class="container">
        <div class="header">
            <h1>Google Sheets Data Migration Tool</h1>
            <p>Process broker sheets and migrate data with formatting and summary</p>
        </div>
    </div>
    """
    
    # HTML template for summary
    def create_summary_html(result):
        if not result or not isinstance(result, dict):
            return "<div class='error'>No results to display</div>"
        
        if not result.get('success', False):
            error_html = f"<div class='error'><h3>❌ Processing Failed</h3><p>{result.get('message', 'Unknown error')}</p></div>"
            
            # Add error logs if available
            if 'errors' in result and result['errors']:
                error_html += "<h4>Errors:</h4><ul>"
                for error in result['errors']:
                    error_html += f"<li>[{error['timestamp']}] {error['message']}</li>"
                error_html += "</ul>"
            
            return error_html
        
        # Success summary
        html = f"""
        <div class='success'>
            <h3>✅ Processing Completed Successfully</h3>
            <p>Processed {result.get('processed_records', 0)} records from {result.get('processed_sheets', 0)} sheets in {result.get('duration_seconds', 0):.2f} seconds</p>
        </div>
        
        <div class='summary-section'>
            <h3>Summary</h3>
            <table class='summary-table'>
                <tr>
                    <th>Metric</th>
                    <th>Value</th>
                </tr>
                <tr>
                    <td>Source Spreadsheet</td>
                    <td>{result.get('source_spreadsheet', 'N/A')}</td>
                </tr>
                <tr>
                    <td>Target Spreadsheet</td>
                    <td>
                        <a href='{result.get('target_spreadsheet_url', '#')}' target='_blank' class='spreadsheet-link'>
                            {result.get('target_spreadsheet', 'N/A')} 🔗
                        </a>
                    </td>
                </tr>
                <tr>
                    <td>Total Sheets</td>
                    <td>{result.get('total_sheets', 0)}</td>
                </tr>
                <tr>
                    <td>Processed Sheets</td>
                    <td>{result.get('processed_sheets', 0)}</td>
                </tr>
                <tr>
                    <td>Total Records</td>
                    <td>{result.get('total_records', 0)}</td>
                </tr>
                <tr>
                    <td>Processed Records</td>
                    <td>{result.get('processed_records', 0)}</td>
                </tr>
                <tr>
                    <td>Companies Processed</td>
                    <td>{result.get('companies_processed', 0)}</td>
                </tr>
                <tr>
                    <td>Total Amount</td>
                    <td>${result.get('total_amount', 0):,.2f}</td>
                </tr>
                <tr>
                    <td>Processing Time</td>
                    <td>{result.get('duration_seconds', 0):.2f} seconds</td>
                </tr>
                <tr>
                    <td>Errors</td>
                    <td>{result.get('errors', 0)}</td>
                </tr>
            </table>
        </div>
        """
        
        # Add log section if logs are available
        if 'logs' in result and result['logs']:
            html += "<div class='log-section'><h3>Processing Logs</h3>"
            for log in result['logs']:
                log_class = 'error' if log.get('type') == 'error' else 'info'
                html += f"<div class='{log_class}'>[{log.get('timestamp', '')}] {log.get('message', '')}</div>"
            html += "</div>"
        
        return html
    
    # Function to update logs in real-time
    def update_logs():
        logs_html = "<div class='log-section'><h3>Processing Logs</h3>"
        for log in processing_status['logs']:
            log_class = 'error' if log.get('type') == 'error' else 'info'
            logs_html += f"<div class='{log_class}'>[{log.get('timestamp', '')}] {log.get('message', '')}</div>"
        logs_html += "</div>"
        return logs_html
    
    # Function to process the spreadsheets and update UI
    def process(source_id, target_id, progress=gr.Progress()):
        # Clear previous logs
        logs_output = update_logs()
        
        # Process spreadsheets
        result = process_spreadsheets(source_id, target_id, progress)
        
        # Update logs again with final status
        logs_output = update_logs()
        
        # Create summary HTML
        summary_html = create_summary_html(result)
        
        return logs_output, summary_html
    
    # Create Gradio interface
    with gr.Blocks(css=css) as interface:
        gr.HTML(html_template)
        
        with gr.Row():
            with gr.Column():
                gr.Markdown("### Source Spreadsheet")
                source_id = gr.Textbox(label="Enter Source Spreadsheet ID or Name", placeholder="Spreadsheet ID is recommended")
            
            with gr.Column():
                gr.Markdown("### Target Spreadsheet")
                target_id = gr.Textbox(label="Enter Target Spreadsheet ID or Name (leave empty to create new)", placeholder="Leave empty to create new spreadsheet")
        
        process_btn = gr.Button("Start Processing", variant="primary")
        
        logs_output = gr.HTML(label="Processing Logs")
        summary_output = gr.HTML(label="Processing Summary")
        
        process_btn.click(
            fn=process,
            inputs=[source_id, target_id],
            outputs=[logs_output, summary_output]
        )
    
    return interface

# Main function to run in Google Colab
def main():
    # Print welcome message
    print("Google Sheets Data Migration Tool")
    print("==================================")
    print("This tool processes broker sheets from a source Google Spreadsheet")
    print("and migrates data to a target spreadsheet with formatting and summary.")
    print("\nStarting Gradio interface...")
    
    # Create and launch Gradio interface
    interface = create_interface()
    interface.launch(share=True, debug=True)

# Run the main function when executed in Google Colab
if __name__ == "__main__":
    main()